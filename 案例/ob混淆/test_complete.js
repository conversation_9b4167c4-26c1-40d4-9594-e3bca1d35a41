// 测试完整依赖收集
function _0x3c9d(){var _0x420b1a=["WXuLt","rDJmF","7425464CaGNVd","split","njZEd","NpfLS","rixxZ","HBCom","lpDte","excep","LmSOV","vBSLp","\x5c(\x20*\x5c","ikqCu","mQtcC","$]*)","trace","aacMp","ogRcz","227894WPaVuK","YBQve","fYgSE","GZESJ","fVDfk","nsHbk","YXIfY","XnfhA","GePHp","tion","BAlBk","XuEYx","wPYUa","hhXqK","IevgB","LoEGj","eKSYL","gmeiU","setIn","uBNCy","undef","pRXqv","cAoXb","CVdmU","Z_$][","iZQCB","proto","QIAXy","GUYYo","OyJwL","bxYys","JYFxx","xlcAg","RHTea","log","pqbHt","Jpxpd","pySrq","ZSplg","mlGzo","oAlrS","ing","nHKdo","PDVyE","hHMxq","5215bwzFae","IsClU","XvlSL","StLbE","|2|3|","dSCpc","UlHgH","RVNJz",")+)+)","qlyYO","jZBYN","AcwdZ","qZuEr","Qqnyp","TnXod","EriJj","MOBfK","jtNAm","jhRog","IwvYY","IaaBS","Xjnjb","QTivB","rVDmg","ekgDY","usjIs","19393902dblLOg","DTSkb","__pro","lengt","gyHYG","Ppzan","RVIDJ","UIbPC","lYUbn","GWzDU","JbqsP","IClOq","tCqGr","rQYhd","table","QZamw","ion\x20*","QoYDY","RUOPg","YXEEe","mKppr","objec","cfTvh","VgatR","mapOp","faBuu","KWZIA","80KWXrvo","FgPnH","OOqFq","JPZnV","dAkbp","gpyBI","xpVlr","rUsyp","pYUls","1|0|4","lXdWH","pKCul","fleRw","qQkjF","rfEku","XgFBt","LvKiH","jTjdo","Hello","zVBYG","test","AizgT","WQlrY","toStr","lvFay","YdjCO","YOjuW","tGvtD","UepeY","4|3|1","YsIQu","leCvc","gVabX","wdbyK","zMvLz","CTDsi","DbATF","ViQuG","0-9a-","QPfnc","IvrNK","ynKfv","THHtI","|4|2|","UogqC","jmLQl","ion","oEauM","bind","ApGMq","DZrxn","zKHIK","tBrTc","MtvTp","HuRBs","ructo","DbtdG","SvILn","Sqcdi","pvgFO","XnJNo","dOMoD","kMtgY","Ongzq","mhubP","svEYL","zA-Z_","warn","error","jDOow","oCkon","jmZCQ","TpOqa","QACpH","bwvda","qssRZ","VppSS","\x5c+\x5c+\x20","hKoJS","BvvhL","a-zA-","vShYj","DQpDX","tIrNG","info","to__","gGlbH","strin","ZZjDI","AEhST","DbEGY","PnAgE","init","56397qcODLj","vsmOI","WxqUu","JVHgx","tpXRx","DnlKP","oRoyE","vuNZM","YkAiG","terva","purAP","myKri","wDYFs","6923cqeybJ","xLFLI","Nfmjq","dHkAR","QNmjl","lhRrG","lplgk","124cNXiRy","kOziq","Mhfvh","LmEpr","vLXqy","lQlyf","LnFYG","\x20Worl","type","ined","tLDyI","chain","*(?:[","FCroH","coovn","1184634wIvqvI","IFklE","const","Oqaea","lvrxw","apply","cxZwm","AxHPS","hcmXP","KAbkz","FTjdU","fCDtX","jYcpT","rNOgs","zngqn","aLaon","vsGQW","WAjZO","funct","wbjiV","(((.+","Jzvhd","oVsfG","NerAH","SeKUK","uFpoD","rbKyd","VAzkb","|0|2|","eHEkm","qAkJs","YUPDh","CXJzn","Qdrgi","FLAhQ","conso","yrpod","eGRhx","yFLjH","pLOHU","oHfeg","oVOyj","opffE","gwRgJ","QYLbr","zRZqt","RAyMx","2uGdzEO","GxUHs","MtvuH","GwKUb","searc","QennM","Cxzzt","qgFCq","nLdJE","XMRUz","qsveh","JeBEz","qEskJ","jBpjT","dMMGE","6348QtACxT","5|3|1","JCWSn","WvMXp","CPhjX","YRhEC","xcERn","iOuSi","NKjuM","input","ryJKM","TwWDA","lqojt","bacoQ","rkWgb","RGnhX","OkLbG","jMAyu","waJro","KNSKo","AedDH","IrQnv","DNPWH","BwVBJ","TDsZs"];_0x3c9d=function(){return _0x420b1a};return _0x3c9d()};
(function(_0x4b7322,_0x406886){function _0x46d4ec(_0x10f726,_0x3856af,_0x395fd0,_0x2bcc4e,_0x44ee51){return _0x2ec1(_0x3856af-573,_0x44ee51)}function _0x246c3c(_0x4faa40,_0x46f2ca,_0x5bf070,_0x35f131,_0x2ab625){return _0x2ec1(_0x5bf070- -889,_0x35f131)}var _0x1eff71=_0x4b7322();function _0x2e89aa(_0x5012e5,_0x6958bd,_0x1a7a85,_0xda52b8,_0x40915a){return _0x2ec1(_0x1a7a85-837,_0xda52b8)}function _0x243fba(_0x4c1ddf,_0x1f91b7,_0x38cc2c,_0x41768c,_0x56d4a0){return _0x2ec1(_0x4c1ddf- -778,_0x56d4a0)}function _0x2c3f66(_0x2667e6,_0xa978cd,_0x204ca0,_0x524394,_0xf704c7){return _0x2ec1(_0xa978cd- -402,_0x524394)}while(!![]){try{var _0x1228a7=-parseInt(_0x2c3f66(32,119,184,-44,-21))/(1913+-5321+1*3409)+-parseInt(_0x2e89aa(1421,1183,1299,1294,1304))/(-1286*-3+4402+-2*4129)*(-parseInt(_0x246c3c(-118,-133,-177,-209,-291))/(-13*-181+-4525+5*435))+parseInt(_0x2c3f66(19,-2,-26,152,-8))/(439*11+-2884+3*-647)*(-parseInt(_0x2c3f66(78,164,138,294,91))/(-3911*-1+-7019+3113*1))+-parseInt(_0x243fba(-301,-202,-140,-143,-460))/(-577*1+2645*1+-2062)*(parseInt(_0x2c3f66(-136,-9,-157,86,7))/(-5153+-7441+12601))+-parseInt(_0x243fba(-274,-134,-408,-246,-179))/(3249+-982*-1+103*-41)+parseInt(_0x243fba(-363,-409,-469,-305,-398))/(-5527+-587*-11+-921)*(parseInt(_0x246c3c(-274,-267,-270,-123,-394))/(6980+2286+-356*26))+parseInt(_0x2e89aa(1311,1357,1429,1303,1336))/(-6161+-1718+-2630*-3);if(_0x1228a7===_0x406886){break}else{_0x1eff71["push"](_0x1eff71["shift"]())}}catch(_0x41fbc2){_0x1eff71["push"](_0x1eff71["shift"]())}}}(_0x3c9d,-672329+-816901+53573*39));

function _0x2ec1(_0x3d560c, _0x5cfc02) {
    var _0x1ea56b = _0x3c9d();
    return _0x2ec1 = function (_0x4fe2ca, _0x21219b) {
        _0x4fe2ca = _0x4fe2ca - (0x5 * 0x749 + -0x184b + -0xa9e);
        var _0x510a94 = _0x1ea56b[_0x4fe2ca];
        return _0x510a94;
    }, _0x2ec1(_0x3d560c, _0x5cfc02);
}

function _0x157a79(_0x45ec54, _0x1660b0, _0x287235, _0xe3963a, _0x368e79) {
    return _0x2ec1(_0xe3963a - -0x107, _0x45ec54);
}

// 测试调用
console.log('测试结果:', _0x157a79(0xd3, 0x21, 0x7e, 0xa1, 0xe8));
