const parse = require('@babel/parser')
const fs = require('fs')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;

process.argv.length > 2 ? File1 = process.argv[2] : File1 = './_encode.js'
process.argv.length > 3 ? File2 = process.argv[2] : File2 = './_decode.js'

var ast = parse.parse(fs.readFileSync(File1, {encoding: 'utf-8'}));
//收集满足需求的方法
// function _0x579b6e(_0x2d5da7, _0x3f8238, _0x1baccb, _0x261ed5, _0x5e4a5d) {
//     return _0x3f9d(_0x2d5da7 - -0x3b9, _0x3f8238);
// }

var func_code = ''
var func_name = []

traverse(ast,{
    FunctionDeclaration(path){
        var {id,params,body} = path.node;
        if (params.length != 5 || body.body.length != 1){
            return ;
        }
        if (!types.isReturnStatement(body.body[0])){
            return ;
        }
        func_code += path.toString()
        func_name.push(id.name)
    }
})
console.log(func_code)
console.log(func_name)
//执行方法  把方法都加载到当前的环境
eval(func_code)
//找到调用的位置  满足需求
traverse(ast,{
    CallExpression(path) {
        var {callee, arguments} = path.node;
        if (!types.isIdentifier(callee) || !func_name.includes(callee.name)){
            return ;
        }
        if (!arguments.length == 5 || !isNodeLiteral(arguments)){
            return ;
        }
        var value = eval(path.toString())
        path.replaceWith(types.valueToNode(value))
    }
})

// let {code} = generator(ast);
// console.log(code);
// fs.writeFile(File2, code, (err) => {
// });
