function _0xda28() {
    var _0x3c73af = ['call', 'CqHgd', 'TRDmo', 'n\x20(fu', 'liyAh', 'XjxTF', 'itwdP', 'KXaMH', '5|4|0', 'pcBOc', 'dOuSZ', 'PsBmJ', 'UBFiz', 'ExHgf', 'Objec', 'sUvGc', 'eaFaG', 'sUhFG', '$]*)', 'wqIzh', 'ffdar', 'wHwfe', 'mPiEr', 'UNGUq', 'UJDaV', '2jgouUT', 'bNeLd', 'BwYvT', 'toStr', 'vZkqc', 'EZydE', 'MNaBt', 'JtCdb', 'fmpHB', 'dkkiB', 'searc', 'YrfoW', 'DIiIk', 'WpiMl', 'QrhFK', 'XIGQP', 'njCDa', 'to__', 'xzrSj', 'Mhspa', 'vRnIr', 'yxWcJ', 'qIOka', 'JFbkd', 'ROpOZ', 'state', 'Qoqrx', 'BLLva', 'e)\x20{}', 'bYajo', 'iXOuY', 'terva', 'log', 'JdOpE', 'RswgA', 'mYmiE', 'BuErX', 'gcaxn', 'abPQg', 'kcntt', '9LFqgsm', '3971888ZzritP', '\x22retu', 'DEVqA', '\x20(tru', 'SxInZ', 'SHyib', 'sJzYm', '533158slTnbb', 'rFzDe', 'JyYgX', 'OLhja', 'apply', 'ion\x20*', 'nnlqp', 'bDHcC', 'AOquu', 'zVVdM', 'LxPRQ', 'LTKqK', 'TQPbZ', 'gAaCH', 'IITeP', 'retur', 'ThkWH', 'sjJXJ', 'bZUoH', 'kEKmL', 'nVjUi', '{}.co', 'lengt', 'dSkuo', 'ieCqS', 'pjncX', 'aWNFp', 'dMSQN', 'iZfBH', 'tchos', 'CmNEo', 'sbFBw', 'WVkmy', 'APEMy', 'zviyt', 'nVhKi', 'bhjok', 'ing', 'warn', 'VPidI', 'VAVlR', 'KFCDU', 'YyjOx', 'fYcmm', 'TvmXw', 'vNcTe', 'MjqAY', 'fbQkc', 'DkOxH', 'YyUZn', 'zgGlR', 'MBXdi', 'conso', 'wdAJq', 'UGdqH', ')+)+)', 'nctio', 'info', 'ctor(', 'UeDUf', 'LlcPL', 'JTwaJ', 'eWKbL', 'tion', 'SIPop', 'is\x22)(', 'KhCEe', 'sUPQJ', 'actio', '*(?:[', 'proto', 'xJThq', 'btoxv', 'EnRRk', 'KVgzU', 'jraGD', 'CJjJJ', 'nJYBi', 'pqfUl', 'qqWyp', 'NXruV', 'PKGSQ', 'oykAP', 'suVzv', 'Blggo', 'lRVeL', 'sTZgN', 'GBKhF', 'vcvEL', 'rn\x20th', 'uTnJt', 'GSQWV', 'BEVjT', 'fdZYX', 'debu', 'rhTnM', 'n()\x20', 'AzdcO', 'while', 'EtoFg', 'ylvCD', 'Iydhn', 'vchNd', 'yJZvj', 'FZjpr', 'pLfSK', 'GGBRj', 'funct', 'kOPDS', 'msWNa', 'EPRYa', 'AlaiO', 'tWKzC', 'qNoil', 'LEKsK', 'Z_$][', 'ZMPDi', 'TvcFM', 'blqDr', 'RLdHc', 'jMeFf', 'MMofL', 'mbodq', 'vliRs', 'mXLHw', 'CCXVV', 'vLmOa', 'split', 'MNDNk', 'error', 'pHflR', 'zqplh', 'FfheW', 'UtrwL', 'QlERW', 'type', 'setIn', 'lApzm', 'CEDoj', 'eGJwW', 'oFHzv', 'rURMS', 'ziRED', 'VKDql', 'rvNmq', '1806508sgTABi', 'ZMaSW', 'JVaFX', 'WoiVp', 'trace', 'QLUPk', 'cCBEW', 'xIOGy', 'init', 'fWGuL', 'JbeDl', 'count', 'GTpup', 'GmIIS', '154044OvtcAs', 'oVQyM', 'hyWju', 'Slrzz', 'eFwAF', 'cQTFI', 'IjPOE', 'DXMSl', 'PuZBw', 'aSPWI', '\x20Worl', 'RHAcK', 'nstru', 'CqqfE', 'aOSPs', 'YNgLp', 'a-zA-', 'bBCMx', 'wDSws', 'XuwcM', 'flauy', 'cIVBq', 'YIuuD', 'lyIwt', 'CDxTB', 'pMmlE', 'TxdEK', 'mTcMU', 'RvPDI', 'pnGMV', 'OCiww', 'ZEPOq', '2774928jIOklv', 'kAGAs', 'dOdZd', 'ZzvDe', 'OMgiU', 'excep', 'const', 'cgeuY', 'LvhBC', 'FSPYw', '443548LcPfXu', 'zNxqi', 'NzAFA', 'table', '|3|1|', 'Hello', 'lynhG', 'ZDWmp', '23631970NOpCms', 'CpSfE', 'MPvje', 'fRvsX', '__pro', 'JeICK', 'PAXOb', 'kkdPV', 'QeEKw', 'NdLir', 'puPMU', 'jPGjS', 'CcQnj', 'FxiUT', 'gger', 'ructo', '0-9a-', 'PXNGu', 'jYwmr', 'jdlqw', 'aatRg', 'bind', 'eLwFB', 'SnLFa', 'JcxwM', 'rrKmS', 'gawas', 'SOnVN', '|4|1|', 'ENSlV', 'axhaz', 'obnAq', 'YjBDO', 'RYUrs', 'GPWqi', 'chain', 'GcbDj', 'ssRCa', '3|2|5', 'PTFpm', 'FAHLM', 'wwrkz', 'ZymlL', 'zkkMR', 'pBNow', 'XJQSa', 'bAdOf', 'QDwFl', 'cCBdJ', 'uITuu', 'input', 'KheOp', 'LuIEr', 'kvhHU', 'rJQLF', 'PhiDc', '5KfBrTm', 'ZVvpY', 'XJrQE', '(((.+', 'cTBIy', 'SKLYL', 'test', 'lChhp', 'bMOYV', 'zA-Z_', 'strin', 'oYEfS', 'YQnHY', 'QWhcw', 'tzQtG', 'NSQJl', 'hKMzb', 'iZNyS', '\x5c+\x5c+\x20', 'QliBm', 'fasow', 'Myfnw', 'opSsC', 'UmpZN', 'SCyol', 'ybwUy', 'tbtzK', 'xSxgY', 'Oxndd', 'QfZWF', 'iusxr', 'XFHVW', '\x5c(\x20*\x5c'];
    _0xda28 = function () {
        return _0x3c73af;
    };
    return _0xda28();
}

function _0x3f9d(_0x5ec33e, _0x4e7433) {
    var _0x570275 = _0xda28();
    return _0x3f9d = function (_0x3f789e, _0x1b4812) {
        _0x3f789e = _0x3f789e - (-0xfeb + -0x1802 + 0x2867);
        var _0x427c70 = _0x570275[_0x3f789e];
        return _0x427c70;
    }, _0x3f9d(_0x5ec33e, _0x4e7433);
}


function _0x579b6e(_0x2d5da7, _0x3f8238, _0x1baccb, _0x261ed5, _0x5e4a5d) {
    return _0x3f9d(_0x2d5da7 - -0x3b9, _0x3f8238);
}

console.log(_0x579b6e(-0x236, -0x260, -0x1f6, -0x2ba, -0x2d2));